{"aadc_info": {"age_group": 0}, "accessibility": {"captions": {"headless_caption_enabled": false, "live_caption_language": "fr-FR"}}, "account_info": [{"access_point": 17, "account_id": "fake-000300006C521E46", "accountcapabilities": {"accountcapabilities/g42tslldmfya": -1, "accountcapabilities/g44tilldmfya": -1, "accountcapabilities/ge2dinbnmnqxa": -1, "accountcapabilities/ge2tkmznmnqxa": -1, "accountcapabilities/ge2tknznmnqxa": -1, "accountcapabilities/ge2tkobnmnqxa": -1, "accountcapabilities/ge3dgmjnmnqxa": -1, "accountcapabilities/ge3dgobnmnqxa": -1, "accountcapabilities/geydgnznmnqxa": -1, "accountcapabilities/geytcnbnmnqxa": -1, "accountcapabilities/gezdcnbnmnqxa": -1, "accountcapabilities/gezdsmbnmnqxa": -1, "accountcapabilities/geztenjnmnqxa": -1, "accountcapabilities/gi2tklldmfya": -1, "accountcapabilities/gu2dqlldmfya": -1, "accountcapabilities/gu4dmlldmfya": -1, "accountcapabilities/guydolldmfya": -1, "accountcapabilities/guzdslldmfya": -1, "accountcapabilities/haytqlldmfya": -1, "accountcapabilities/he4tolldmfya": -1}, "edge_account_age_group": 0, "edge_account_cid": "", "edge_account_environment": 0, "edge_account_environment_string": "login.microsoftonline.com", "edge_account_first_name": "ma<PERSON>ou", "edge_account_is_test_on_premises_profile": false, "edge_account_last_name": "fz", "edge_account_location": "", "edge_account_oid": "", "edge_account_phone_number": "", "edge_account_puid": "", "edge_account_sovereignty": 0, "edge_account_tenant_id": "9188040d-6c67-4c5b-b112-36a304b66dad", "edge_account_type": 5, "edge_tenant_supports_msa_linking": false, "edge_wam_aad_for_app_account_type": 0, "email": "<EMAIL>", "full_name": "", "gaia": "fake-000300006C521E46", "given_name": "", "hd": "", "is_supervised_child": -1, "is_under_advanced_protection": false, "last_downloaded_image_url_with_size": "", "locale": "", "picture_url": ""}], "account_tracker_service_last_update": "*****************", "alternate_error_pages": {"backup": true}, "apps": {"shortcuts_arch": "", "shortcuts_version": 1}, "arbitration_last_notification_shown": "*****************", "arbitration_using_experiment_config": false, "autocomplete": {"retention_policy_last_version": 137}, "autofill": {"last_version_deduped": 137}, "bookmark": {"storage_computation_last_update": "*****************"}, "browser": {"available_dark_theme_options": "All", "chat_v2": {"ip_eligibility_status": {"last_checked_time": "*****************"}}, "edge_sidebar_visibility": {"_game_assist_": {"order": {"8ac719c5-140b-4bf2-a0b7-c71617f1f377": **********}}, "add_app_to_bottom": true, "order": {"8ac719c5-140b-4bf2-a0b7-c71617f1f377": **********}}, "edge_sidebar_visibility_debug": {"order_list": ["<PERSON><PERSON><PERSON>"], "order_raw_data": {"8ac719c5-140b-4bf2-a0b7-c71617f1f377": {"name": "<PERSON><PERSON><PERSON>", "pos": "**********"}}}, "editor_proofing_languages": {"en": {"Grammar": true, "Spelling": true}, "en-US": {"Grammar": true, "Spelling": true}, "fr": {"Grammar": true, "Spelling": true}, "fr-FR": {"Grammar": true, "Spelling": true}}, "gamer_mode_asset_store_prefs": {"779d97ed-2254-4943-a1f3-c811fa709092": {"gamer_mode_modal_script_hash": "xie40asvhdbPXzggtqUJ4lfglpLAYbJeXpWhq51+U+s=", "gamer_mode_modal_script_url": "https://edgeassetservice.azureedge.net/assets/gamer_mode_modal_ux/1.1.69/asset?assetgroup=GamerModeModalUX"}}, "has_seen_welcome_page": false, "hub_app_non_synced_preferences": {"apps": {"0c835d2d-9592-4c7a-8d0a-0e283c9ad3cd": {"last_path": ""}, "168a2510-04d5-473e-b6a0-828815a7ca5f": {"last_path": ""}, "1ec8a5a9-971c-4c82-a104-5e1a259456b8": {"last_path": ""}, "2354565a-f412-4654-b89c-f92eaa9dbd20": {"last_path": ""}, "2caf0cf4-ea42-4083-b928-29b39da1182b": {"last_path": ""}, "380c71d3-10bf-4a5d-9a06-c932e4b7d1d8": {"last_path": ""}, "439642fc-998d-4a64-8bb6-940ecaf6b60b": {"last_path": ""}, "523b5ef3-0b10-4154-8b62-10b2ebd00921": {"last_path": ""}, "64be4f9b-3b81-4b6e-b354-0ba00d6ba485": {"last_path": ""}, "698b01b4-557a-4a3b-9af7-a7e8138e8372": {"last_path": ""}, "76b926d6-3738-46bf-82d7-2ab896ddf70b": {"last_path": ""}, "7b52ae05-ae84-4165-b083-98ba2031bc22": {"last_path": ""}, "8682d0fa-50b3-4ece-aa5b-e0b33f9919e2": {"last_path": ""}, "8ac719c5-140b-4bf2-a0b7-c71617f1f377": {"last_path": ""}, "92f1b743-e26b-433b-a1ec-912d1f0ad1fa": {"last_path": ""}, "96defd79-4015-4a32-bd09-794ff72183ef": {"last_path": ""}, "9ce3c9c2-462f-4cc9-bbd7-57d656445be0": {"last_path": ""}, "c814ae4d-fa0a-4280-a444-cb8bd264828b": {"last_path": ""}, "cd4688a9-e888-48ea-ad81-76193d56b1be": {"last_path": ""}, "d3ff4c56-a2b8-4673-ad13-35e7706cc9d1": {"last_path": ""}, "da15ec1d-543d-41c9-94b8-eb2bd060f2c7": {"last_path": ""}, "dadd1f1c-380c-4871-9e09-7971b6b15069": {"last_path": ""}, "e6723537-66ff-4f4e-ab56-a4cbaddf4e0f": {"last_path": ""}}}, "hub_app_preferences": {}, "hub_cleanup_candidate_list_for_debug": [{"cleanup_progress": "cleanup_start_v2"}, {"cleanup_progress": "cleanup_done_v2"}, {"cleanup_progress": "cleanup_start_v2"}, {"cleanup_progress": "skipped_cleanup_v2_has_happened"}, {"cleanup_progress": "cleanup_start_v2"}, {"cleanup_progress": "skipped_cleanup_v2_has_happened"}, {"cleanup_progress": "cleanup_start_v2"}, {"cleanup_progress": "skipped_cleanup_v2_has_happened"}, {"cleanup_progress": "cleanup_start_v2"}, {"cleanup_progress": "skipped_cleanup_v2_has_happened"}, {"cleanup_progress": "cleanup_start_v2"}, {"cleanup_progress": "skipped_cleanup_v2_has_happened"}, {"cleanup_progress": "cleanup_start_v2"}, {"cleanup_progress": "skipped_cleanup_v2_has_happened"}, {"cleanup_progress": "cleanup_start_v2"}, {"cleanup_progress": "skipped_cleanup_v2_has_happened"}, {"cleanup_progress": "cleanup_start_v2"}, {"cleanup_progress": "skipped_cleanup_v2_has_happened"}], "hub_cleanup_context_v2": {"cleanup_debug_info_v2_adjusted_engaged_app_count": 0, "cleanup_debug_info_v2_app_count_threshold": 1, "cleanup_debug_info_v2_current_sidebar_visibility": 0, "cleanup_debug_info_v2_discover_icon_enabled": true, "cleanup_debug_info_v2_dwell_time_in_secs": 10, "cleanup_debug_info_v2_engaged_app_count": 0, "cleanup_debug_info_v2_expected_sidebar_visibility": 0, "cleanup_debug_info_v2_is_tower_off_by_user": false, "cleanup_debug_info_v2_skip_user_generated_apps_for_threshold": true, "cleanup_debug_info_v2_user_generated_app_count": 0, "hub_app_cleanup_v2_done": true}, "recent_theme_color_list": [4293914607.0, 4293914607.0, 4293914607.0, 4293914607.0, 4293914607.0], "show_hub_app_in_sidebar_buttons": {"8ac719c5-140b-4bf2-a0b7-c71617f1f377": 0}, "show_hub_app_in_sidebar_buttons_legacy": {"8ac719c5-140b-4bf2-a0b7-c71617f1f377": 0}, "show_hub_app_in_sidebar_buttons_legacy_update_time": "13393213990826546", "time_of_last_normal_window_close": "13393270675941231", "underside_chat_bing_signed_in_status": false, "user_level_features_context": {}, "window_placement": {"bottom": 816, "left": 100, "maximized": false, "right": 616, "top": 24, "work_area_bottom": 804, "work_area_left": 0, "work_area_right": 1536, "work_area_top": 0}}, "browser_content_container_height": 704, "browser_content_container_width": 502, "browser_content_container_x": 0, "browser_content_container_y": 81, "commerce_daily_metrics_last_update_time": "13393270627231607", "countryid_at_install": 18002, "custom_links": {"list": []}, "default_search_provider": {"choice_screen_random_shuffle_seed": "7397504571949038091", "guid": ""}, "devtools": {"preferences": {"EdgeDevToolsLayoutInfo": {"current_dock_state": 0, "horizontal_size": 300, "showEmulationMode": false, "vertical_size": 555}}}, "domain_diversity": {"last_reporting_timestamp": "*****************"}, "dual_engine": {"consumer_mode": {"enabled_state": 1}, "consumer_site_list_with_ie_entries": false, "consumer_sitelist_location": "https://go.microsoft.com/fwlink/?linkid=2133855&bucket=59", "consumer_sitelist_version": "97", "external_consumer_shared_cookie_data": {}, "profile_id": "H6GVDBBB", "sitelist_has_consumer_data": true}, "edge": {"account_type": 5, "bookmarks": {"last_dup_info_record_time": "*****************"}, "msa_sso_info": {"allow_for_non_msa_profile": true}, "profile_matches_os_primary_account": false, "profile_sso_info": {"aad_sso_algo_state": 1, "is_first_profile": true, "is_msa_first_profile": true, "msa_sso_algo_state": 1}, "profile_sso_option": 1, "services": {"last_gaia_id": "fake-000300006C521E46", "signin_scoped_device_id": "a4ee7f87-8ebb-4e8b-8572-b14be48030e6"}, "workspaces": {"state": "{\"edgeWorkspacePrefsVersion\":2,\"enableFluid\":true,\"failedRestartSpaceId\":\"\",\"failedToConnectToFluid\":false,\"fluidStatus\":0,\"fre_shown\":false,\"fromCache\":false,\"isFluidPreferencesConnected\":false,\"isSpaceOpening\":false,\"openingSpaceId\":\"\",\"statusForScreenReaders\":\"\",\"workspacePopupMode\":0,\"workspacesForExternalLinks\":[]}"}}, "edge_rewards": {"cache_data": "CAA=", "coachmark_promotions": {}, "hva_promotions": [], "refresh_status_muted_until": "*****************"}, "edge_ux_config": {"assignmentcontext": "ZvRsKcXXoY739pqXVXDJ90zWE1haxnPMH0ZgE5k55NM=", "dataversion": "*********", "experimentvariables": {"2f717976": {"edgeServerUX.sync.historyDataTypeEnabled": true}, "shop-60c": {"edgeServerUX.shopping.aablockth": 60, "edgeServerUX.shopping.block99": false}, "shopppdismisstreatment": {"edgeServerUX.shopping.msEdgeShoppingCashbackDismissTimeout2s": true}, "shoprevenuattributionc": {"edgeServerUX.shopping.disableCashbackOnCouponCopy": false}}, "flights": {"2f717976": "31213786", "shop-60c": "31271455", "shopppdismisstreatment": "31004791", "shoprevenuattributionc": "31235886"}, "latestcorrelationid": "Ref A: 6C941D9FD0904D748DBF2F692150EEA3 Ref B: MIL30EDGE0710 Ref C: 2025-06-01T16:57:08Z"}, "edge_wallet": {"passwords": {"password_lost_report_date": "13393270657044701"}, "trigger_funnel": {"records": []}}, "enterprise_profile_guid": "ea11098d-7270-4335-8cb0-27c99e036346", "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}, "last_chrome_version": "137.0.3296.52", "pdf_upsell_triggered": false, "pinned_extension_migration": true, "pinned_extensions": []}, "family_safety": {"activity_reporting_enabled": false, "web_filtering_enabled": false}, "fsd": {"retention_policy_last_version": 137}, "gaia_cookie": {"changed_time": **********.781534, "hash": "2jmj7l5rSw0yVb/vlWAYkK/YBwk=", "last_list_accounts_data": "[\"gaia.l.a.r\",[]]"}, "gcm": {"product_category_for_subtypes": "com.chrome.windows"}, "google": {"services": {"consented_to_sync": true, "signin": {"LAST_SIGNIN_ACCESS_POINT": {"time": "2025-05-31T11:51:09.440Z", "value": "17"}}, "signin_scoped_device_id": "831ed0d6-cdcf-48e6-85b0-b45c529b21c8"}}, "in_product_help": {"new_badge": {"Compose": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeProactiveNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "LensOverlay": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}}, "recent_session_enabled_time": "*****************", "recent_session_start_times": ["*****************"], "session_last_active_time": "*****************", "session_start_time": "*****************"}, "intl": {"accept_languages": "fr-FR,fr,en-US,en", "selected_languages": "fr-FR,fr,en-US,en"}, "invalidation": {"per_sender_topics_to_handler": {"1013309121859": {}}}, "language_dwell_time_average": {"en": 4.0}, "language_model_counters": {"en": 1}, "language_usage_count": {"en": 1}, "media": {"engagement": {"schema_version": 5}}, "media_router": {"receiver_id_hash_token": "s8swk6Cko59dknOLCp6192iomF1NAPFQPHn/cMGPzG3G8FIjm/x5s3p+xmAqMqE7X2owopYb4dXGaTbPCUMjug=="}, "muid": {"last_sync": "13393270627225840", "values_seen": []}, "ntp": {"next_site_suggestions_available": false, "num_personal_suggestions": 1}, "optimization_guide": {"hintsfetcher": {"hosts_successfully_fetched": {}}, "predictionmodelfetcher": {"last_fetch_attempt": "13393131215282467", "last_fetch_success": "13393131218184060"}, "previous_optimization_types_with_filter": {"AMERICAN_EXPRESS_CREDIT_CARD_FLIGHT_BENEFITS": true, "AMERICAN_EXPRESS_CREDIT_CARD_SUBSCRIPTION_BENEFITS": true, "AUTOFILL_ABLATION_SITES_LIST1": true, "AUTOFILL_ABLATION_SITES_LIST2": true, "AUTOFILL_ABLATION_SITES_LIST3": true, "AUTOFILL_ABLATION_SITES_LIST4": true, "AUTOFILL_ABLATION_SITES_LIST5": true, "AUTOFILL_PREDICTION_IMPROVEMENTS_ALLOWLIST": true, "BMO_CREDIT_CARD_AIR_MILES_PARTNER_BENEFITS": true, "BMO_CREDIT_CARD_ALCOHOL_STORE_BENEFITS": true, "BMO_CREDIT_CARD_DINING_BENEFITS": true, "BMO_CREDIT_CARD_DRUGSTORE_BENEFITS": true, "BMO_CREDIT_CARD_ENTERTAINMENT_BENEFITS": true, "BMO_CREDIT_CARD_GROCERY_BENEFITS": true, "BMO_CREDIT_CARD_OFFICE_SUPPLY_BENEFITS": true, "BMO_CREDIT_CARD_RECURRING_BILL_BENEFITS": true, "BMO_CREDIT_CARD_TRANSIT_BENEFITS": true, "BMO_CREDIT_CARD_TRAVEL_BENEFITS": true, "BMO_CREDIT_CARD_WHOLESALE_CLUB_BENEFITS": true, "BUY_NOW_PAY_LATER_ALLOWLIST_AFFIRM": true, "BUY_NOW_PAY_LATER_ALLOWLIST_ZIP": true, "CAPITAL_ONE_CREDIT_CARD_BENEFITS_BLOCKED": true, "CAPITAL_ONE_CREDIT_CARD_DINING_BENEFITS": true, "CAPITAL_ONE_CREDIT_CARD_ENTERTAINMENT_BENEFITS": true, "CAPITAL_ONE_CREDIT_CARD_GROCERY_BENEFITS": true, "CAPITAL_ONE_CREDIT_CARD_STREAMING_BENEFITS": true, "EWALLET_MERCHANT_ALLOWLIST": true, "FORMS_ANNOTATIONS": true, "GLIC_ACTION_PAGE_BLOCK": true, "HISTORY_CLUSTERS": true, "HISTORY_EMBEDDINGS": true, "IBAN_AUTOFILL_BLOCKED": true, "PIX_MERCHANT_ORIGINS_ALLOWLIST": true, "PIX_PAYMENT_MERCHANT_ALLOWLIST": true, "SHARED_CREDIT_CARD_DINING_BENEFITS": true, "SHARED_CREDIT_CARD_ENTERTAINMENT_BENEFITS": true, "SHARED_CREDIT_CARD_FLIGHT_BENEFITS": true, "SHARED_CREDIT_CARD_GROCERY_BENEFITS": true, "SHARED_CREDIT_CARD_STREAMING_BENEFITS": true, "SHARED_CREDIT_CARD_SUBSCRIPTION_BENEFITS": true, "SHOPPING_PAGE_PREDICTOR": true, "TEXT_CLASSIFIER_ENTITY_DETECTION": true, "VCN_MERCHANT_OPT_OUT_DISCOVER": true, "VCN_MERCHANT_OPT_OUT_MASTERCARD": true, "VCN_MERCHANT_OPT_OUT_VISA": true}, "previously_registered_optimization_types": {"ABOUT_THIS_SITE": true, "HISTORY_CLUSTERS": true, "LOADING_PREDICTOR": true, "MERCHANT_TRUST_SIGNALS_V2": true, "PRICE_TRACKING": true, "V8_COMPILE_HINTS": true}, "store_file_paths_to_delete": {}}, "password_manager": {"autofillable_credentials_account_store_login_database": false, "autofillable_credentials_profile_store_login_database": false, "profile_store_migrated_to_os_crypt_async": true}, "personalization_data_consent": {"personalization_in_context_consent_can_prompt": false, "personalization_in_context_count": 0}, "privacy_sandbox": {"fake_notice": {"prompt_shown_time": "*****************", "prompt_shown_time_sync": "*****************"}, "first_party_sets_data_access_allowed_initialized": true}, "profile": {"avatar_index": 26, "content_settings": {"exceptions": {"3pcd_heuristics_grants": {}, "3pcd_support": {}, "abusive_notification_permissions": {}, "access_to_get_all_screens_media_in_session": {}, "anti_abuse": {}, "app_banner": {}, "ar": {}, "are_suspicious_notifications_allowlisted_by_user": {}, "auto_picture_in_picture": {}, "auto_select_certificate": {}, "automatic_downloads": {}, "automatic_fullscreen": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "camera_pan_tilt_zoom": {}, "captured_surface_control": {}, "clear_browsing_data_cookies_exceptions": {}, "client_hints": {"https://maps.app.goo.gl:443,*": {"last_modified": "13393209429669780", "setting": {"client_hints": [9, 10, 11, 13, 14, 16, 23, 25, 29]}}, "https://www.google.com:443,*": {"last_modified": "13393270670112422", "setting": {"client_hints": [4, 5]}}}, "clipboard": {}, "controlled_frame": {}, "cookie_controls_metadata": {"http://localhost,*": {"last_modified": "13393270627379165", "setting": {}}, "https://[*.]goo.gl,*": {"last_modified": "1339*************", "setting": {}}, "https://[*.]google.com,*": {"last_modified": "*****************", "setting": {}}}, "cookies": {}, "direct_sockets": {}, "direct_sockets_private_network_access": {}, "display_media_system_audio": {}, "disruptive_notification_permissions": {}, "durable_storage": {}, "edge_ad_targeting": {}, "edge_ad_targeting_data": {}, "edge_sdsm": {}, "edge_split_screen": {}, "edge_u2f_api_request": {}, "edge_user_agent_token": {}, "fedcm_idp_registration": {}, "fedcm_idp_signin": {"https://accounts.google.com:443,*": {"last_modified": "*****************", "setting": {"chosen-objects": [{"idp-origin": "https://accounts.google.com", "idp-signin-status": false}]}}}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_access_extended_permission": {}, "file_system_access_restore_permission": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "formfill_metadata": {}, "geolocation": {"http://localhost:51527,*": {"last_modified": "*****************", "last_visit": "*****************", "setting": 1}, "http://localhost:62850,*": {"last_modified": "*****************", "last_visit": "*****************", "setting": 1}}, "hand_tracking": {}, "hid_chooser_data": {}, "hid_guard": {}, "http_allowed": {}, "https_enforced": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "insecure_private_network": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "javascript_optimizer": {}, "keyboard_lock": {}, "legacy_cookie_access": {}, "legacy_cookie_scope": {}, "local_fonts": {}, "local_network_access": {}, "media_engagement": {"http://localhost:49457,*": {"expiration": "13400905708883222", "last_modified": "13393129708883227", "lifetime": "7776000000000", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:50840,*": {"expiration": "13400906290299085", "last_modified": "13393130290299094", "lifetime": "7776000000000", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:51234,*": {"expiration": "13400906985438447", "last_modified": "13393130985438457", "lifetime": "7776000000000", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:51316,*": {"expiration": "13400960227422256", "last_modified": "13393184227422267", "lifetime": "7776000000000", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:51527,*": {"expiration": "13400988193216661", "last_modified": "13393212193216675", "lifetime": "7776000000000", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:52250,*": {"expiration": "13400922545120949", "last_modified": "13393146545120973", "lifetime": "7776000000000", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:55193,*": {"expiration": "13400990295989816", "last_modified": "13393214295989830", "lifetime": "7776000000000", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:60947,*": {"expiration": "13400955485556947", "last_modified": "13393179485556957", "lifetime": "7776000000000", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:62535,*": {"expiration": "13400945653486916", "last_modified": "13393169653486922", "lifetime": "7776000000000", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:62850,*": {"expiration": "13400985523586940", "last_modified": "13393209523586951", "lifetime": "7776000000000", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:64097,*": {"expiration": "13401046675938097", "last_modified": "13393270675938105", "lifetime": "7776000000000", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:64204,*": {"expiration": "13400942153973166", "last_modified": "13393166153973175", "lifetime": "7776000000000", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:64344,*": {"expiration": "13400956448937555", "last_modified": "13393180448937568", "lifetime": "7776000000000", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:64979,*": {"expiration": "13400905072767384", "last_modified": "13393129072767393", "lifetime": "7776000000000", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://maps.app.goo.gl:443,*": {"expiration": "13400985523582759", "last_modified": "13393209523582771", "lifetime": "7776000000000", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://www.google.com:443,*": {"expiration": "13401046671909932", "last_modified": "13393270671909937", "lifetime": "7776000000000", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}}, "media_stream_camera": {}, "media_stream_mic": {}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notification_permission_review": {}, "notifications": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {"http://localhost:51527,*": {"last_modified": "13393211874761532", "setting": {"Geolocation": {"dismiss_count": 1}}}}, "permission_autorevocation_data": {}, "pointer_lock": {}, "popups": {}, "private_network_chooser_data": {}, "private_network_guard": {}, "protected_media_identifier": {}, "protocol_handler": {}, "reduced_accept_language": {}, "safe_browsing_url_check_data": {}, "secure_network": {}, "secure_network_sites": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "site_engagement": {"http://localhost:49457,*": {"last_modified": "13393270627185224", "setting": {"lastEngagementTime": 1.3393163044663964e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 14.999999999999995, "rawScore": 14.999999999999995}}, "http://localhost:50840,*": {"last_modified": "13393270627185207", "setting": {"lastEngagementTime": 1.3393163614906656e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 4.8, "rawScore": 4.8}}, "http://localhost:51234,*": {"last_modified": "13393270627185190", "setting": {"lastEngagementTime": 1.3393163848857832e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 3.6, "rawScore": 3.6}}, "http://localhost:51316,*": {"last_modified": "13393270627185174", "setting": {"lastEngagementTime": 1.3393210861873192e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 8.999999999999998}}, "http://localhost:51527,*": {"last_modified": "13393270627185157", "setting": {"lastEngagementTime": 1.3393239589103908e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 5.999999999999999, "rawScore": 5.999999999999999}}, "http://localhost:52250,*": {"last_modified": "13393270627185141", "setting": {"lastEngagementTime": 1.339316459907794e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 5.999999999999999, "rawScore": 5.999999999999999}}, "http://localhost:55193,*": {"last_modified": "13393270627185124", "setting": {"lastEngagementTime": 1.339324182718474e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 7.199999999999998, "rawScore": 7.199999999999998}}, "http://localhost:60947,*": {"last_modified": "13393270627185108", "setting": {"lastEngagementTime": 1.3393206794916688e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 5.999999999999999}}, "http://localhost:62535,*": {"last_modified": "13393270627185091", "setting": {"lastEngagementTime": 1.3393197189782208e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 4.8, "rawScore": 4.8}}, "http://localhost:62850,*": {"last_modified": "13393270627185074", "setting": {"lastEngagementTime": 1.3393236998748228e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 6.599999999999999, "rawScore": 6.599999999999999}}, "http://localhost:64097,*": {"last_modified": "13393270657634342", "setting": {"lastEngagementTime": 1.3393270657634316e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 4.2, "rawScore": 4.2}}, "http://localhost:64204,*": {"last_modified": "13393270627185057", "setting": {"lastEngagementTime": 1.3393193480161732e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 5.999999999999999, "rawScore": 5.999999999999999}}, "http://localhost:64344,*": {"last_modified": "13393270627185035", "setting": {"lastEngagementTime": 1.3393207979155372e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 4.8}}, "http://localhost:64979,*": {"last_modified": "13393270627184888", "setting": {"lastEngagementTime": 1.3393162400404728e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 5.999999999999999, "rawScore": 5.999999999999999}}}, "sleeping_tabs": {}, "sound": {}, "speaker_selection": {}, "ssl_cert_decisions": {}, "storage_access": {}, "storage_access_header_origin_trial": {}, "subresource_filter": {}, "subresource_filter_data": {}, "tech_scam_detection": {}, "third_party_storage_partitioning": {}, "top_level_3pcd_origin_trial": {}, "top_level_3pcd_support": {}, "top_level_storage_access": {}, "trackers": {}, "trackers_data": {"https://googleads.g.doubleclick.net:443,*": {"last_modified": "13393270648997119", "setting": {"count": 2}}, "https://play.google.com:443,*": {"last_modified": "13393270650393631", "setting": {"count": 2}}, "https://ssl.gstatic.com:443,*": {"last_modified": "13393209430684794", "setting": {"count": 1}}, "https://static.doubleclick.net:443,*": {"last_modified": "13393270649003360", "setting": {"count": 2}}, "https://www.google.com:443,*": {"last_modified": "13393270649080450", "setting": {"count": 2}}, "https://www.youtube.com:443,*": {"last_modified": "13393270642768394", "setting": {"count": 2}}, "https://yt3.ggpht.com:443,*": {"last_modified": "13393270649095731", "setting": {"count": 2}}}, "tracking_org_exceptions": {}, "tracking_org_relationships": {}, "tracking_protection": {}, "unused_site_permissions": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "web_app_installation": {}, "webid_api": {}, "webid_auto_reauthn": {}, "window_placement": {}}, "permission_actions": {"geolocation": [{"action": 0, "prompt_disposition": 1, "time": "13393209446792660"}, {"action": 2, "prompt_disposition": 1, "time": "13393211874761488"}, {"action": 0, "prompt_disposition": 1, "time": "13393211883024531"}]}, "pref_version": 1}, "created_by_version": "136.0.7103.116", "creation_time": "13393128534865113", "did_work_around_bug_364820109_default": true, "did_work_around_bug_364820109_exceptions": true, "edge_crash_exit_count": 0, "edge_password_is_using_new_login_db_path": false, "edge_password_login_db_path_flip_flop_count": 0, "edge_profile_id": "f8d22fae-ac9d-4e27-b66c-b496a14b7761", "edge_user_with_non_zero_passwords": false, "exit_type": "Normal", "family_member_role": "not_in_family", "is_relative_to_aad": false, "last_engagement_time": "13393270657634317", "last_time_obsolete_http_credentials_removed": 1748654994.907714, "last_time_password_store_metrics_reported": 1748797057.043778, "managed": {"locally_parent_approved_extensions": {}, "locally_parent_approved_extensions_migration_state": 1}, "managed_user_id": "", "name": "Votre Chrome", "network_pbs": {}, "observed_session_time": {"feedback_rating_in_product_help_observed_session_time_key_137.0.3296.52": 920.0}, "one_time_permission_prompts_decided_count": 3, "password_hash_data_list": [], "were_old_google_logins_removed": true}, "reset_prepopulated_engines": false, "safebrowsing": {"advanced_protection_last_refresh": "13393270627225369", "event_timestamps": {}, "extension_telemetry_file_data": {}, "hash_real_time_ohttp_expiration_time": "13393387799046029", "hash_real_time_ohttp_key": "0wAg+hMNl1Hq6PLEyremD9QrQyOzGuwzjXtsFsp/K5pW9F8ABAABAAI=", "metrics_last_log_time": "13393128534", "scout_reporting_enabled_when_deprecated": false}, "safety_hub": {"unused_site_permissions_revocation": {"migration_completed": true}}, "saved_tab_groups": {"did_enable_shared_tab_groups_in_last_session": false, "specifics_to_data_migration": true}, "segmentation_platform": {"client_result_prefs": "ClIKDXNob3BwaW5nX3VzZXISQQo2DQAAAAAQ8pDC6Kuf5RcaJAocChoNAAAAPxIMU2hvcHBpbmdVc2VyGgVPdGhlchIEEAIYBCADELyRwuirn+UX", "device_switcher_util": {"result": {"labels": ["NotSynced"]}}, "last_db_compaction_time": "13393036799000000", "uma_in_sql_start_time": "13393128534904655"}, "sessions": {"event_log": [{"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13393146545110055", "type": 2, "window_count": 1}, {"crashed": false, "time": "13393165861389555", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13393166153956888", "type": 2, "window_count": 1}, {"crashed": false, "time": "13393169270798976", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13393169653478550", "type": 2, "window_count": 1}, {"crashed": false, "time": "13393179126009345", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13393179485545064", "type": 2, "window_count": 1}, {"crashed": false, "time": "13393180189053330", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13393180448919154", "type": 2, "window_count": 1}, {"crashed": false, "time": "13393183173334123", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13393184227410388", "type": 2, "window_count": 1}, {"crashed": false, "time": "13393209374059529", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 2, "time": "13393209523568220", "type": 2, "window_count": 1}, {"crashed": false, "time": "13393211845296310", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13393212193204182", "type": 2, "window_count": 1}, {"crashed": false, "time": "13393213870580688", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "*****************", "type": 2, "window_count": 1}, {"crashed": false, "time": "*****************", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "*****************", "type": 2, "window_count": 1}], "session_data_status": 3}, "settings": {"force_google_safesearch": false}, "shopping": {"contextual_features_enabled": true, "dma_telemetry_expiration_time": "*****************", "last_pwilo_api_fetch_time": "*****************"}, "signin": {"accounts_metadata_dict": {"fake-000300006C521E46": {"BookmarksExplicitBrowserSigninEnabled": false, "ExtensionsExplicitBrowserSigninEnabled": false}}, "allowed": true, "cookie_clear_on_exit_migration_notice_complete": true, "signin_with_explicit_browser_signin_on": true, "sync_paused_start_time": "*****************"}, "smart_explore": {"auto_cleanup": {"check_time": "*****************", "noengagement_since": "*****************"}}, "spellcheck": {"dictionaries": ["fr-FR", "fr"], "dictionary": ""}, "sync": {"apps": true, "autofill": true, "bookmarks": true, "collections": true, "collections_edge_re_evaluated": true, "collections_edge_supported": true, "data_type_status_for_sync_to_signin": {"app_list": false, "app_settings": false, "apps": false, "arc_package": false, "autofill": false, "autofill_profiles": false, "autofill_valuable": false, "autofill_wallet": false, "autofill_wallet_credential": false, "autofill_wallet_metadata": false, "autofill_wallet_offer": false, "autofill_wallet_usage": false, "bookmarks": false, "collaboration_group": false, "contact_info": false, "cookies": false, "device_info": false, "dictionary": false, "extension_settings": false, "extensions": false, "history": false, "history_delete_directives": false, "incoming_password_sharing_invitation": false, "managed_user_settings": false, "nigori": false, "os_preferences": false, "os_priority_preferences": false, "outgoing_password_sharing_invitation": false, "passwords": false, "plus_address": false, "plus_address_setting": false, "power_bookmark": false, "preferences": false, "printers": false, "printers_authorization_servers": false, "priority_preferences": false, "product_comparison": false, "reading_list": false, "saved_tab_group": false, "search_engines": false, "security_events": false, "send_tab_to_self": false, "sessions": false, "shared_tab_group_account_data": false, "shared_tab_group_data": false, "sharing_message": false, "themes": false, "user_consent": false, "user_events": false, "web_apps": false, "webapks": false, "webauthn_credential": false, "wifi_configurations": false, "workspace_desk": false}, "edge_account_type": 5, "edge_wallet": true, "edge_wallet_edge_supported": true, "encryption_bootstrap_token_per_account_migration_done": true, "extensions": true, "extensions_edge_supported": true, "feature_status_for_sync_to_signin": 5, "history_edge_supported": true, "keep_everything_synced": false, "passwords": true, "passwords_per_account_pref_migration_done": true, "preferences": true, "tabs": true, "tabs_edge_supported": true, "typed_urls": true}, "syncing_theme_prefs_migrated_to_non_syncing": true, "tab_group_saves_ui_update_migrated": true, "tab_groups": [], "toolbar": {"pinned_chrome_labs_migration_complete": true}, "toolbar_declutter": {"new_user_cleanup_triggered": true, "undo": {"last_time": "*****************"}}, "total_passwords_available_for_account": 0, "total_passwords_available_for_profile": 0, "translate_ignored_count_for_language": {"en": 1}, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "user_experience_metrics": {"personalization_data_consent_enabled_last_known_value": false}, "video_enhancement": {"mode": "Non-AI enhancement"}, "visual_search": {"dma_state": 1}, "web_apps": {"did_migrate_default_chrome_apps": ["MigrateDefaultChromeAppToWebAppsGSuite", "MigrateDefaultChromeAppToWebAppsNonGSuite"], "last_preinstall_synchronize_version": "137", "link_handling_info": {"enabled_for_installed_apps": true}}}